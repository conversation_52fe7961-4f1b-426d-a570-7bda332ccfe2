import React, { memo, useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  TableBody,
  TableFooter,
  Button,
  Tooltip,
  IconButton,
  TableSortLabel,
  Box,
  Checkbox,
  CircularProgress
} from '@mui/material';
import DownloadIcon from '../common/DownloadIcon';
import {
  StyledTableContainer,
  StyledTable,
  StyledTableHead,
  StyledHeaderCell,
  StyledTableCell,
  StyledTableRow
} from '../common/TablePresets';
import { Task } from '../../types/job';
import { getFileTypeLabel, hasFilesOfType } from '../../utils/jobUtils';
import TaskRow from './TaskRow';

import BulkActionsToolbar, { BulkTaskStatus } from './BulkActionsToolbar';
import { createMultiKMLbulkTask, downloadJobResult } from '@/services/jobService';
import { useBulkTaskStatus } from '@/lib/hooks/useBulkTaskStatus';

import { Job } from '../../types/job'; // Changed Batch to Job

interface TasksTableProps {
  tasks: Task[];
  orderBy: string;
  order: 'asc' | 'desc';
  selectedTaskIds: number[];
  downloadingFiles: Record<string, boolean>;
  onSort: (property: string) => void;
  onToggleTaskSelection: (taskId: number) => void;
  onToggleAllTasksSelection: () => void;
  onDownloadFile: (taskId: number, result: any) => void;
  onDownloadTaskFiles: (taskId: number) => void;
  onDownloadAllOfType: (type: string) => void;
  onDownloadAll: () => void;
  onOpenSupportDialog: (taskId: number, datasetName: string, jobId: string) => void; // Changed batchId to jobId
  jobId: string; // Changed batchId to jobId
  job: Job; // Changed batch to job
}

const TasksTable: React.FC<TasksTableProps> = ({
  tasks,
  orderBy,
  order,
  selectedTaskIds,
  downloadingFiles,
  onSort,
  onToggleTaskSelection,
  onToggleAllTasksSelection,
  onDownloadFile,
  onDownloadTaskFiles,
  onDownloadAllOfType,
  onDownloadAll,
  onOpenSupportDialog,
  jobId, // Changed batchId to jobId
  job // Changed batch to job
}) => {
  const fileTypes = ['kml', 'csv', 'pdf'];
  
  // State for bulk job management
  const [bulkTaskId, setbulkTaskId] = useState<string | null>(null);
  const { status: bulkTaskStatus, error: bulkTaskError, downloadResult } = useBulkTaskStatus(bulkTaskId);
  
  // Check if all visible tasks are selected
  const allSelected = tasks.length > 0 && selectedTaskIds.length === tasks.length;
  const someSelected = selectedTaskIds.length > 0 && selectedTaskIds.length < tasks.length;
  
  // Reset bulk job when selection changes and a job is complete or has error
  useEffect(() => {
    if ((bulkTaskStatus === 'complete' || bulkTaskStatus === 'error') && bulkTaskId) {
      setbulkTaskId(null);
    }
  }, [selectedTaskIds, bulkTaskStatus, bulkTaskId]);

  // Handle downloading all selected task files
  const handleDownloadSelected = () => {
    selectedTaskIds.forEach(taskId => {
      onDownloadTaskFiles(taskId);
    });
  };

  // Handle creating a multi-KML file from selected tasks
  const handleCreateMultiKML = async () => {
    if (selectedTaskIds.length === 0) return;
    
    try {
      // Reset any previous job
      if (bulkTaskStatus === 'complete' || bulkTaskStatus === 'error') {
        setbulkTaskId(null);
      }
      
      // Create a new bulk job
      const firstSelectedTask = tasks.find(task => task.id === selectedTaskIds[0]);
      const multiKmlName = firstSelectedTask?.dataset?.name?.replace(/[^a-zA-Z0-9_.-]/g, '_') || 'UnknownDataset';
      // Use template name from the first task in the job as jobName proxy
      const jobName = job.tasks?.[0]?.global_job_template?.name?.replace(/[^a-zA-Z0-9_.-]/g, '_') || 'UnknownJob'; // Changed batch.jobs to job.tasks
      const taskCount = selectedTaskIds.length;
      const outputFilename = `${multiKmlName}_${jobName}_${taskCount}.kml`;

      const { id } = await createMultiKMLbulkTask(selectedTaskIds, outputFilename); // Pass filename
      setbulkTaskId(id);
      
      // If the job is already complete (possible in demo/mock implementation)
      if (bulkTaskStatus === 'complete') {
        await downloadResult();
      }
    } catch (error) {
      console.error('Failed to create multi-KML job:', error);
    }
  };

  return (
    <StyledTableContainer
      role="region"
      aria-label="Job tasks"
      tabIndex={0}
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '80vh',
        maxHeight: '80vh'
      }}
    >
      <BulkActionsToolbar
        selectedIds={selectedTaskIds}
        downloadingFiles={downloadingFiles}
        onDownloadSelected={handleDownloadSelected}
        bulkTaskStatus={bulkTaskStatus}
        onCreateMultiKML={handleCreateMultiKML}
        bulkTaskError={bulkTaskError}
      />

      <StyledTable sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
        {/* Fixed Header */}
        <StyledTableHead sx={{ flexShrink: 0 }}>
          <StyledTableRow>
            <StyledHeaderCell padding="checkbox" sx={{ width: '48px' }}>
              <Checkbox
                indeterminate={someSelected}
                checked={allSelected}
                onChange={onToggleAllTasksSelection}
                inputProps={{ 'aria-label': 'select all tasks' }}
                size="small"
              />
            </StyledHeaderCell>
            <StyledHeaderCell sx={{ width: '80px' }}>
              <TableSortLabel
                active={orderBy === 'id'}
                direction={orderBy === 'id' ? order : 'asc'}
                onClick={() => onSort('id')}
              >
                ID
              </TableSortLabel>
            </StyledHeaderCell>
            <StyledHeaderCell sx={{ width: '200px' }}>
              <TableSortLabel
                active={orderBy === 'dataset'}
                direction={orderBy === 'dataset' ? order : 'asc'}
                onClick={() => onSort('dataset')}
              >
                Dataset
              </TableSortLabel>
            </StyledHeaderCell>
            <StyledHeaderCell sx={{ width: '120px' }}>
              <TableSortLabel
                active={orderBy === 'status'}
                direction={orderBy === 'status' ? order : 'asc'}
                onClick={() => onSort('status')}
              >
                Status
              </TableSortLabel>
            </StyledHeaderCell>
            <StyledHeaderCell sx={{ width: '80px' }}>Support</StyledHeaderCell>
            <StyledHeaderCell sx={{ width: '140px' }}>
              <TableSortLabel
                active={orderBy === 'created_at'}
                direction={orderBy === 'created_at' ? order : 'asc'}
                onClick={() => onSort('created_at')}
              >
                Created At
              </TableSortLabel>
            </StyledHeaderCell>
            <StyledHeaderCell sx={{ width: '100px' }}>Downloads</StyledHeaderCell>
            {fileTypes.map((type) => (
              <StyledHeaderCell key={type} align="center" sx={{ width: '80px' }}>
                <Typography variant="subtitle2" color="text.secondary">
                  {getFileTypeLabel(type)}
                </Typography>
              </StyledHeaderCell>
            ))}
            <StyledHeaderCell align="center" sx={{ width: '100px' }}>
              <Typography variant="subtitle2" color="text.secondary">
                All Files
              </Typography>
            </StyledHeaderCell>
          </StyledTableRow>
        </StyledTableHead>

        {/* Scrollable Body */}
        <TableBody sx={{
          flexGrow: 1,
          overflow: 'auto',
          display: 'block',
          '& tr': {
            display: 'table',
            width: '100%',
            tableLayout: 'fixed'
          }
        }}>
          {tasks.map((task) => (
            <TaskRow
              key={task.id}
              task={task}
              isSelected={selectedTaskIds.includes(task.id)}
              onToggleSelect={onToggleTaskSelection}
              onDownloadFile={onDownloadFile}
              onDownloadTaskFiles={onDownloadTaskFiles}
              onOpenSupportDialog={(taskId, datasetName) => onOpenSupportDialog(taskId, datasetName, jobId)} // Changed batchId to jobId
              downloadingFiles={downloadingFiles}
              jobId={jobId} // Changed batchId to jobId
            />
          ))}
        </TableBody>

        {/* Fixed Footer */}
        <TableFooter sx={{ flexShrink: 0 }}>
          <StyledTableRow sx={{
            borderTop: '1px solid',
            borderColor: 'divider',
            backgroundColor: 'action.hover'
          }}>
            <StyledTableCell colSpan={7} sx={{ width: '668px' }}>
              <Typography variant="body2" color="text.secondary">
                Batch Downloads
              </Typography>
            </StyledTableCell>
            {fileTypes.map((type) => {
              if (!hasFilesOfType(tasks, type)) {
                return <StyledTableCell key={type} align="center" sx={{ width: '80px' }} />;
              }
              return (
                <StyledTableCell key={type} align="center" sx={{ width: '80px' }}>
                  <Tooltip title={`Download all ${getFileTypeLabel(type)} files from all tasks`}>
                    <span>
                      <IconButton
                        size="medium"
                        onClick={() => onDownloadAllOfType(type)}
                        disabled={downloadingFiles[`type-${type}`]}
                        aria-label={`Download all ${getFileTypeLabel(type)} files`}
                        sx={{
                          color: 'text.secondary',
                          '&:hover': {
                            color: 'primary.main'
                          }
                        }}
                      >
                        {downloadingFiles[`type-${type}`] ? (
                          <CircularProgress size={20} />
                        ) : (
                          <DownloadIcon fileType={type} isMulti={true} />
                        )}
                      </IconButton>
                    </span>
                  </Tooltip>
                </StyledTableCell>
              );
            })}
            <StyledTableCell align="center" sx={{ width: '100px' }}>
              <Tooltip title="Download all files from all tasks">
                <span>
                  <IconButton
                    size="medium"
                    onClick={onDownloadAll}
                    disabled={!tasks.some(t => t.task_results && t.task_results.length > 0) || downloadingFiles['all']}
                    aria-label="Download all files from all tasks"
                    sx={{
                      color: tasks.some(t => t.task_results && t.task_results.length > 0) ? 'text.secondary' : 'action.disabled',
                      '&:hover': {
                        color: tasks.some(t => t.task_results && t.task_results.length > 0) ? 'primary.main' : 'action.disabled'
                      },
                      display: { xs: 'inline-flex', xl: 'none' }
                    }}
                  >
                    {downloadingFiles['all'] ? (
                      <CircularProgress size={20} />
                    ) : (
                      <DownloadIcon fileType="all" isMulti={true} />
                    )}
                  </IconButton>
                  <Button
                    size="medium"
                    variant="outlined"
                    startIcon={
                      downloadingFiles['all'] ?
                      <CircularProgress size={20} /> :
                      <DownloadIcon fileType="all" isMulti={true} />
                    }
                    onClick={onDownloadAll}
                    disabled={!tasks.some(t => t.task_results && t.task_results.length > 0) || downloadingFiles['all']}
                    sx={{
                      color: tasks.some(t => t.task_results && t.task_results.length > 0) ? 'text.secondary' : 'action.disabled',
                      '&:hover': {
                        color: tasks.some(t => t.task_results && t.task_results.length > 0) ? 'primary.main' : 'action.disabled'
                      },
                      display: { xs: 'none', xl: 'inline-flex' },
                      '.MuiButton-startIcon': {
                        marginLeft: '4px'
                      }
                    }}
                  >
                    Download All
                  </Button>
                </span>
              </Tooltip>
            </StyledTableCell>
          </StyledTableRow>
        </TableFooter>
      </StyledTable>
    </StyledTableContainer>
  );
};

export default memo(TasksTable);
